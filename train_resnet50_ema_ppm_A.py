import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
import numpy as np
from sklearn.metrics import accuracy_score, cohen_kappa_score, roc_auc_score
from sklearn.metrics import precision_recall_fscore_support
from tqdm import tqdm
import json
import os
from datetime import datetime

from dataloader_APTOS import APTOSDataLoader
from models.Resnet50_EMA_PPM_A import ResNet50_EMA_PPM_Sequential

# 获取当前脚本文件名（不含扩展名）
SCRIPT_NAME = os.path.splitext(os.path.basename(__file__))[0]


class DRTrainer_EMA_PPM_Sequential:
    def __init__(self, num_classes=5, batch_size=32, lr=1e-4, epochs=30,
                 ema_factor=8, ppm_dim=256, pool_scales=(1, 2, 3, 6)):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'mps' if torch.backends.mps.is_available() else 'cpu')
        self.num_classes = num_classes
        self.epochs = epochs
        self.ema_factor = ema_factor
        self.ppm_dim = ppm_dim
        self.pool_scales = pool_scales

        # 数据加载
        self.dataloader = APTOSDataLoader(batch_size=batch_size)
        self.train_loader, self.val_loader, self.test_loader = self.dataloader.get_dataloaders()

        # 模型 - 使用ResNet50_EMA_PPM_Sequential
        self.model = ResNet50_EMA_PPM_Sequential(
            num_classes=num_classes,
            ema_factor=ema_factor,
            ppm_dim=ppm_dim,
            pool_scales=pool_scales
        ).to(self.device)

        # 冻结backbone，只训练EMA、PPM和融合模块
        self._freeze_backbone()

        # 优化器和调度器
        self.optimizer = optim.Adam(self.model.parameters(), lr=lr)
        self.scheduler = CosineAnnealingLR(self.optimizer, T_max=epochs)
        self.criterion = nn.CrossEntropyLoss()

        self.best_qwk = 0.0

        # 训练历史记录
        self.training_history = {
            'train_loss': [],
            'val_accuracy': [],
            'val_qwk': [],
            'val_auc': [],
            'val_precision': [],
            'val_recall': [],
            'val_f1': [],
            'learning_rate': [],
            'epochs': [],
            'best_epoch': 0,
            'best_qwk': 0.0
        }
        
        # 测试结果记录
        self.test_results = None

    def _freeze_backbone(self):
        """冻结backbone，只训练EMA、PPM和融合模块"""
        # 冻结ResNet50的所有层，除了最后的全连接层
        for name, param in self.model.model.named_parameters():
            if 'fc' not in name:  # 除了最后的全连接层，冻结其他所有参数
                param.requires_grad = False
            else:  # 解冻最后的分类层
                param.requires_grad = True
        
        # 确保所有EMA模块的参数可训练
        for param in self.model.ema_layer1.parameters():
            param.requires_grad = True
        for param in self.model.ema_layer2.parameters():
            param.requires_grad = True
        for param in self.model.ema_layer3.parameters():
            param.requires_grad = True
        for param in self.model.ema_layer4.parameters():
            param.requires_grad = True
            
        # 确保PPM模块的参数可训练
        for param in self.model.ppm.parameters():
            param.requires_grad = True
            
        # 确保特征融合层的参数可训练
        for param in self.model.feature_fusion.parameters():
            param.requires_grad = True

    def train_epoch(self):
        self.model.train()
        total_loss = 0

        for images, labels in tqdm(self.train_loader, desc="Training"):
            images, labels = images.to(self.device), labels.to(self.device)

            self.optimizer.zero_grad()
            outputs = self.model(images)
            loss = self.criterion(outputs, labels)
            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()

        return total_loss / len(self.train_loader)

    def evaluate(self, loader):
        self.model.eval()
        all_preds, all_labels, all_probs = [], [], []

        with torch.no_grad():
            for images, labels in loader:
                images, labels = images.to(self.device), labels.to(self.device)
                outputs = self.model(images)
                probs = torch.softmax(outputs, dim=1)
                preds = torch.argmax(outputs, dim=1)

                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probs.extend(probs.cpu().numpy())

        return np.array(all_preds), np.array(all_labels), np.array(all_probs)

    def calculate_metrics(self, preds, labels, probs):
        """计算评估指标"""
        # 基础指标
        acc = accuracy_score(labels, preds)
        qwk = cohen_kappa_score(labels, preds, weights='quadratic')

        # AUC (one-vs-rest)
        auc = roc_auc_score(labels, probs, multi_class='ovr', average='macro')

        # 整体precision, recall, f1 (weighted average)
        precision, recall, f1, _ = precision_recall_fscore_support(
            labels, preds, average='weighted', zero_division=0
        )

        return {
            'accuracy': acc,
            'qwk': qwk,
            'auc': auc,
            'precision': precision,
            'recall': recall,
            'f1': f1
        }

    def save_training_metrics(self, filename=None):
        """保存训练指标到JSON文件"""
        # 确保results文件夹存在
        os.makedirs('results', exist_ok=True)

        filename = f'{SCRIPT_NAME}_metrics.json'

        filepath = os.path.join('results', filename)

        # 添加训练配置信息
        training_config = {
            'model': 'ResNet50_EMA_PPM_Sequential',
            'num_classes': self.num_classes,
            'epochs': self.epochs,
            'batch_size': self.dataloader.batch_size,
            'device': str(self.device),
            'optimizer': 'Adam',
            'scheduler': 'CosineAnnealingLR',
            'criterion': 'CrossEntropyLoss',
            'ema_factor': self.ema_factor,
            'ppm_dim': self.ppm_dim,
            'pool_scales': list(self.pool_scales),
            'use_residual': True,  # 默认使用残差连接
            'integration_scheme': 'Sequential Integration (方案A)',
            'innovation': 'EMA (local multi-scale attention) → PPM (global context aggregation)',
            'advantages': [
                'Complementary features: local details + global context',
                'Reasonable parameter increase (~1.5M)',
                'Simple implementation and stable training',
                'Best balance of performance and efficiency'
            ]
        }

        # 准备保存的数据，测试结果放在最前面
        save_data = {
            'timestamp': datetime.now().isoformat()
        }
        
        # 如果有测试结果，放在最前面
        if self.test_results is not None:
            save_data['test_results'] = self.test_results
            
        # 然后是训练历史
        save_data['training_history'] = self.training_history
        
        # 最后是训练配置
        save_data['training_config'] = training_config

        # 保存到JSON文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=4, ensure_ascii=False)

        print(f"训练指标已保存到: {filepath}")
        return filepath

    def train(self):
        print(f"开始训练ResNet50_EMA_PPM_Sequential模型，设备: {self.device}")
        print(f"EMA factor: {self.ema_factor}, PPM dim: {self.ppm_dim}")
        print(f"Pool scales: {self.pool_scales}, Use residual: True")
        print("方案A: 串行集成 - EMA局部注意力 → PPM全局上下文")

        for epoch in range(self.epochs):
            # 训练
            train_loss = self.train_epoch()

            # 验证
            val_preds, val_labels, val_probs = self.evaluate(self.val_loader)
            val_metrics = self.calculate_metrics(val_preds, val_labels, val_probs)

            # 获取当前学习率
            current_lr = self.optimizer.param_groups[0]['lr']

            # 记录训练指标
            self.training_history['train_loss'].append(train_loss)
            self.training_history['val_accuracy'].append(val_metrics['accuracy'])
            self.training_history['val_qwk'].append(val_metrics['qwk'])
            self.training_history['val_auc'].append(val_metrics['auc'])
            self.training_history['val_precision'].append(val_metrics['precision'])
            self.training_history['val_recall'].append(val_metrics['recall'])
            self.training_history['val_f1'].append(val_metrics['f1'])
            self.training_history['learning_rate'].append(current_lr)
            self.training_history['epochs'].append(epoch + 1)

            # 更新学习率
            self.scheduler.step()

            # 保存最佳模型
            if val_metrics['qwk'] > self.best_qwk:
                self.best_qwk = val_metrics['qwk']
                self.training_history['best_qwk'] = self.best_qwk
                self.training_history['best_epoch'] = epoch + 1
                # 确保models_pth文件夹存在
                os.makedirs('models_pth', exist_ok=True)
                model_filename = f'best_{SCRIPT_NAME}.pth'
                torch.save(self.model.state_dict(), f'./models_pth/{model_filename}')

            # 打印结果
            print(f"Epoch {epoch + 1}/{self.epochs}")
            print(f"Train Loss: {train_loss:.4f}")
            print(f"Val - Acc: {val_metrics['accuracy']:.4f}, QWK: {val_metrics['qwk']:.4f}, "
                  f"AUC: {val_metrics['auc']:.4f}, F1: {val_metrics['f1']:.4f}")
            print(f"Learning Rate: {current_lr:.6f}")
            print("-" * 60)

    def test(self):
        """测试集评估"""
        print("加载最佳模型进行测试...")
        model_filename = f'best_{SCRIPT_NAME}.pth'
        self.model.load_state_dict(torch.load(f'./models_pth/{model_filename}'))

        test_preds, test_labels, test_probs = self.evaluate(self.test_loader)
        test_metrics = self.calculate_metrics(test_preds, test_labels, test_probs)

        print("=== 测试集结果 ===")
        print(f"Accuracy: {test_metrics['accuracy']:.4f}")
        print(f"QWK: {test_metrics['qwk']:.4f}")
        print(f"AUC: {test_metrics['auc']:.4f}")
        print(f"Precision: {test_metrics['precision']:.4f}")
        print(f"Recall: {test_metrics['recall']:.4f}")
        print(f"F1: {test_metrics['f1']:.4f}")

        # 保存测试结果到独立的test_results中
        self.test_results = {
            'accuracy': float(test_metrics['accuracy']),
            'qwk': float(test_metrics['qwk']),
            'auc': float(test_metrics['auc']),
            'precision': float(test_metrics['precision']),
            'recall': float(test_metrics['recall']),
            'f1': float(test_metrics['f1'])
        }

        self.save_training_metrics()

        return test_metrics


if __name__ == "__main__":
    # 训练ResNet50_EMA_PPM_Sequential模型
    trainer = DRTrainer_EMA_PPM_Sequential(
        num_classes=5,
        batch_size=32,
        lr=1e-4,
        epochs=15,
        ema_factor=8,
        ppm_dim=256,  # 推荐使用256以控制参数量
        pool_scales=(1, 2, 3, 6)
    )
    trainer.train()

    # 测试
    trainer.test()
