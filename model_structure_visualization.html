<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ResNet50_EMA_PPM_Interactive 模型结构图</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .model-diagram {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            margin: 40px 0;
        }
        
        .layer-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            min-width: 300px;
        }
        
        .input-layer { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .conv-layer { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .resnet-layer { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .ema-layer { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .ppm-layer { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; }
        .fusion-layer { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .output-layer { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        
        .layer-box {
            padding: 15px 25px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            min-width: 200px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .layer-box:hover {
            transform: translateY(-5px);
        }
        
        .layer-title {
            font-size: 1.2em;
            margin-bottom: 5px;
        }
        
        .layer-details {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .arrow {
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 20px solid #34495e;
            margin: 10px 0;
        }
        
        .parallel-branches {
            display: flex;
            gap: 40px;
            justify-content: center;
            align-items: flex-start;
        }
        
        .branch {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }
        
        .merge-arrow {
            display: flex;
            align-items: center;
            gap: 20px;
            margin: 20px 0;
        }
        
        .merge-line {
            width: 100px;
            height: 3px;
            background: #34495e;
        }
        
        .info-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
            border-left: 5px solid #667eea;
        }
        
        .info-title {
            font-size: 1.4em;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .info-list {
            list-style: none;
            padding: 0;
        }
        
        .info-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            color: #495057;
        }
        
        .info-list li:last-child {
            border-bottom: none;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .component-detail {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        
        .component-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 ResNet50_EMA_PPM_Interactive 模型结构图</h1>
        
        <div class="model-diagram">
            <!-- 输入层 -->
            <div class="layer-group input-layer">
                <div class="layer-box">
                    <div class="layer-title">输入图像</div>
                    <div class="layer-details">Shape: (B, 3, 224, 224)</div>
                </div>
            </div>
            
            <div class="arrow"></div>
            
            <!-- ResNet50 前置层 -->
            <div class="layer-group conv-layer">
                <div class="layer-box">
                    <div class="layer-title">Conv1 + BN + ReLU + MaxPool</div>
                    <div class="layer-details">7×7 conv, stride=2 → (B, 64, 56, 56)</div>
                </div>
            </div>
            
            <div class="arrow"></div>
            
            <!-- Layer1 + EMA -->
            <div class="layer-group resnet-layer">
                <div class="layer-box">
                    <div class="layer-title">ResNet Layer1</div>
                    <div class="layer-details">Bottleneck blocks → (B, 256, 56, 56)</div>
                </div>
                <div class="layer-box ema-layer">
                    <div class="layer-title">+ EMA Module</div>
                    <div class="layer-details">Efficient Multi-scale Attention</div>
                </div>
            </div>
            
            <div class="arrow"></div>
            
            <!-- Layer2 + EMA -->
            <div class="layer-group resnet-layer">
                <div class="layer-box">
                    <div class="layer-title">ResNet Layer2</div>
                    <div class="layer-details">Bottleneck blocks → (B, 512, 28, 28)</div>
                </div>
                <div class="layer-box ema-layer">
                    <div class="layer-title">+ EMA Module</div>
                    <div class="layer-details">Efficient Multi-scale Attention</div>
                </div>
            </div>
            
            <div class="arrow"></div>
            
            <!-- 并行分支：Layer3 和 Layer4 -->
            <div class="parallel-branches">
                <!-- Layer3 分支 -->
                <div class="branch">
                    <div class="layer-group resnet-layer">
                        <div class="layer-box">
                            <div class="layer-title">ResNet Layer3</div>
                            <div class="layer-details">(B, 1024, 14, 14)</div>
                        </div>
                        <div class="layer-box ema-layer">
                            <div class="layer-title">+ EMA Module</div>
                            <div class="layer-details">factor=8</div>
                        </div>
                        <div class="layer-box ppm-layer">
                            <div class="layer-title">Lightweight PPM</div>
                            <div class="layer-details">scales=(1,3,6) → (B, 256, 14, 14)</div>
                        </div>
                    </div>
                </div>
                
                <!-- Layer4 分支 -->
                <div class="branch">
                    <div class="layer-group resnet-layer">
                        <div class="layer-box">
                            <div class="layer-title">ResNet Layer4</div>
                            <div class="layer-details">(B, 2048, 7, 7)</div>
                        </div>
                        <div class="layer-box ema-layer">
                            <div class="layer-title">+ EMA Module</div>
                            <div class="layer-details">factor=8</div>
                        </div>
                        <div class="layer-box ppm-layer">
                            <div class="layer-title">Full PPM</div>
                            <div class="layer-details">scales=(1,2,3,6) → (B, 512, 7, 7)</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 融合箭头 -->
            <div class="merge-arrow">
                <div class="merge-line"></div>
                <div class="arrow"></div>
                <div class="merge-line"></div>
            </div>
            
            <!-- 跨尺度特征融合 -->
            <div class="layer-group fusion-layer">
                <div class="layer-box">
                    <div class="layer-title">Cross-Scale Fusion</div>
                    <div class="layer-details">Channel + Spatial Attention → (B, 512, 7, 7)</div>
                </div>
            </div>
            
            <div class="arrow"></div>
            
            <!-- 全局池化和分类 -->
            <div class="layer-group output-layer">
                <div class="layer-box">
                    <div class="layer-title">Global Average Pooling</div>
                    <div class="layer-details">(B, 512, 7, 7) → (B, 512)</div>
                </div>
                <div class="layer-box">
                    <div class="layer-title">Fully Connected</div>
                    <div class="layer-details">(B, 512) → (B, num_classes)</div>
                </div>
            </div>
        </div>
        
        <!-- 详细信息面板 -->
        <div class="info-panel">
            <div class="info-title">🔧 模型组件详解</div>
            
            <div class="component-detail">
                <div class="component-title">EMA (Efficient Multi-scale Attention) 模块</div>
                <ul class="info-list">
                    <li><span class="highlight">分组处理</span>: 将通道分为8组进行独立处理</li>
                    <li><span class="highlight">空间注意力</span>: 水平和垂直方向的全局平均池化</li>
                    <li><span class="highlight">通道注意力</span>: 1×1和3×3卷积捕获不同尺度特征</li>
                    <li><span class="highlight">权重融合</span>: 通过矩阵乘法和Sigmoid激活生成注意力权重</li>
                </ul>
            </div>
            
            <div class="component-detail">
                <div class="component-title">PPM (Pyramid Pooling Module) 模块</div>
                <ul class="info-list">
                    <li><span class="highlight">多尺度池化</span>: 不同池化尺度捕获多层次上下文</li>
                    <li><span class="highlight">轻量级设计</span>: Layer3使用3个尺度，Layer4使用4个尺度</li>
                    <li><span class="highlight">特征融合</span>: 上采样后拼接并通过1×1卷积融合</li>
                    <li><span class="highlight">维度压缩</span>: 降维处理减少计算复杂度</li>
                </ul>
            </div>
            
            <div class="component-detail">
                <div class="component-title">Cross-Scale Fusion 跨尺度融合</div>
                <ul class="info-list">
                    <li><span class="highlight">通道对齐</span>: 1×1卷积统一Layer3和Layer4的通道数</li>
                    <li><span class="highlight">跨尺度注意力</span>: 学习不同层级特征的重要性权重</li>
                    <li><span class="highlight">空间注意力</span>: 3×3卷积捕获空间相关性</li>
                    <li><span class="highlight">特征融合</span>: 加权融合后通过最终卷积层输出</li>
                </ul>
            </div>
        </div>
        
        <div class="info-panel">
            <div class="info-title">📊 模型特点与优势</div>
            <ul class="info-list">
                <li><span class="highlight">交互式集成</span>: Layer3和Layer4都应用PPM处理，最大化特征利用</li>
                <li><span class="highlight">多尺度感知</span>: EMA模块提供高效的多尺度注意力机制</li>
                <li><span class="highlight">上下文建模</span>: PPM模块捕获不同尺度的上下文信息</li>
                <li><span class="highlight">特征融合</span>: 复杂的跨尺度融合机制整合多层次信息</li>
                <li><span class="highlight">参数效率</span>: 在性能提升的同时控制参数增长</li>
                <li><span class="highlight">适用场景</span>: 特别适合糖尿病视网膜病变等医学图像分类任务</li>
            </ul>
        </div>
    </div>
</body>
</html>
