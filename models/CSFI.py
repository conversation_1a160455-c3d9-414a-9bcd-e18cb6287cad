import torch
import torch.nn as nn
import torch.nn.functional as F


class CrossScaleFeatureInteraction(nn.Module):
    """
    Cross-Scale Feature Interaction (CSFI) Module
    
    This module enables interaction between features from different scales,
    allowing high-level semantic features to guide low-level detail features.
    """
    
    def __init__(self, low_channels, high_channels, reduction=16):
        super(CrossScaleFeatureInteraction, self).__init__()
        
        self.low_channels = low_channels
        self.high_channels = high_channels
        
        # Channel alignment for feature fusion
        if low_channels != high_channels:
            self.channel_align = nn.Conv2d(high_channels, low_channels, 1, bias=False)
        else:
            self.channel_align = nn.Identity()
        
        # Lightweight attention mechanism for feature weighting
        self.attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(low_channels * 2, max(low_channels // reduction, 8), 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(max(low_channels // reduction, 8), low_channels, 1, bias=False),
            nn.Sigmoid()
        )

        # Lightweight feature refinement
        self.refine = nn.Sequential(
            nn.Conv2d(low_channels * 2, low_channels, 1, bias=False),  # 1x1 instead of 3x3
            nn.BatchNorm2d(low_channels),
            nn.ReLU(inplace=True)
        )
        
        # Residual connection weight
        self.gamma = nn.Parameter(torch.zeros(1))
        
    def forward(self, low_feat, high_feat):
        """
        Args:
            low_feat: Low-level features (higher resolution)
            high_feat: High-level features (lower resolution)
        
        Returns:
            Enhanced low-level features
        """
        # Get spatial dimensions
        _, _, h, w = low_feat.size()
        
        # Upsample high-level features to match low-level resolution
        high_feat_up = F.interpolate(high_feat, size=(h, w), mode='bilinear', align_corners=False)
        
        # Channel alignment
        high_feat_aligned = self.channel_align(high_feat_up)
        
        # Feature concatenation
        concat_feat = torch.cat([low_feat, high_feat_aligned], dim=1)
        
        # Generate attention weights
        attention_weights = self.attention(concat_feat)
        
        # Apply attention to low-level features
        attended_low = low_feat * attention_weights
        
        # Feature refinement
        refined_feat = self.refine(concat_feat)
        
        # Residual connection with learnable weight
        output = attended_low + self.gamma * refined_feat
        
        return output


class MultiScaleCSFI(nn.Module):
    """
    Multi-Scale Cross-Scale Feature Interaction Module
    
    Applies CSFI across multiple scale pairs in a hierarchical manner.
    """
    
    def __init__(self, channels_list, reduction=16):
        super(MultiScaleCSFI, self).__init__()
        
        self.num_scales = len(channels_list)
        self.csfi_modules = nn.ModuleList()
        
        # Create CSFI modules for adjacent scale pairs
        for i in range(self.num_scales - 1):
            low_ch = channels_list[i]
            high_ch = channels_list[i + 1]
            self.csfi_modules.append(
                CrossScaleFeatureInteraction(low_ch, high_ch, reduction)
            )
    
    def forward(self, feature_list):
        """
        Args:
            feature_list: List of features from different scales
                         [low_res -> high_res] or [layer1, layer2, layer3, layer4]

        Returns:
            List of enhanced features
        """
        enhanced_features = []

        # Apply CSFI from low-level to high-level
        for i, csfi_module in enumerate(self.csfi_modules):
            low_feat = feature_list[i]
            high_feat = feature_list[i + 1]

            # Cross-scale interaction
            enhanced_low = csfi_module(low_feat, high_feat)
            enhanced_features.append(enhanced_low)

        # Add the last high-level feature (no enhancement for the highest level)
        enhanced_features.append(feature_list[-1])

        return enhanced_features


# Test the CSFI module
if __name__ == '__main__':
    # Test single CSFI module
    print("=== Testing Single CSFI Module ===")
    csfi = CrossScaleFeatureInteraction(256, 512)
    
    low_feat = torch.randn(2, 256, 56, 56)   # Layer1 features
    high_feat = torch.randn(2, 512, 28, 28)  # Layer2 features
    
    output = csfi(low_feat, high_feat)
    print(f"Input low feat: {low_feat.shape}")
    print(f"Input high feat: {high_feat.shape}")
    print(f"Output: {output.shape}")
    
    # Test multi-scale CSFI
    print("\n=== Testing Multi-Scale CSFI ===")
    channels = [256, 512, 1024, 2048]
    multi_csfi = MultiScaleCSFI(channels)
    
    # Simulate ResNet features
    features = [
        torch.randn(2, 256, 56, 56),   # Layer1
        torch.randn(2, 512, 28, 28),   # Layer2
        torch.randn(2, 1024, 14, 14),  # Layer3
        torch.randn(2, 2048, 7, 7),    # Layer4
    ]
    
    enhanced_features = multi_csfi(features)
    
    print("Enhanced features:")
    for i, feat in enumerate(enhanced_features):
        print(f"  Layer{i+1}: {feat.shape}")
    
    # Calculate parameters
    total_params = sum(p.numel() for p in multi_csfi.parameters())
    print(f"\nTotal parameters: {total_params:,}")
